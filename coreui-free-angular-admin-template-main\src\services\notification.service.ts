import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get notifications by user
   * @param userId - User ID
   * @returns Observable with notifications list
   */
  getNotificationsByUser(userId: string): Observable<any> {
    return this.http.get<any>(`${this.apiURL}notifications/${userId}`, httpOptions);
  }

  /**
   * Mark notification as read
   * @param notificationId - Notification ID
   * @returns Observable with update result
   */
  markAsRead(notificationId: string): Observable<any> {
    return this.http.put<any>(`${this.apiURL}notifications/read/${notificationId}`, null, httpOptions);
  }

  /**
   * Create notification
   * @param notificationData - Notification data
   * @returns Observable with creation result
   */
  createNotification(notificationData: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}notifications`, notificationData, httpOptions);
  }

  /**
   * Delete notification
   * @param notificationId - Notification ID
   * @returns Observable with deletion result
   */
  deleteNotification(notificationId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiURL}notifications/${notificationId}`, httpOptions);
  }

  SendSmsNotification(data : any): Observable<any> {
    const endpoint = `${this.apiURL}ptchargement/sendSms`;
    return this.http.post<any>(endpoint, data, httpOptions);
  }

  SendMailNotification(): Observable<any> {
    const endpoint = `${this.apiURL}commande/infoAdmin`;
    return this.http.post<any>(endpoint, null, httpOptions);
  }

  sendReservationMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailReservation`, data, httpOptions);
  }

  sendExpeditionMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailExpedition`, data, httpOptions);
  }

  
  sendDeliveryMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailLivraison`, data, httpOptions);
  }


  
  sendMessage(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}messages`, data, httpOptions);
  }

  /**
   * Send cancellation mail
   * @param data - Cancellation data
   * @returns Promise with sending result
   */
  sendCancellationMail(data: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailAnnulation`, data, httpOptions);
  }

  /**
   * Send reservation departure update mail
   * @param data - Update data
   * @returns Promise with sending result
   */
  sendReservationDepartureUpdateMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailUpdateReservationDepart`, data, httpOptions);
  }

  /**
   * Send reservation arrival update mail
   * @param data - Update data
   * @returns Promise with sending result
   */
  sendReservationArrivalUpdateMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailUpdateReservationArrivee`, data, httpOptions);
  }

  /**
   * Send mails from frontend
   * @param data - Mail data
   * @returns Promise with sending result
   */
  sendMailsFromFrontend(data: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}sendMailsFromFront`, data, httpOptions);
  }
}
