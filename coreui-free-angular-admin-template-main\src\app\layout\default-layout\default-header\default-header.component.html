<!-- <c-header class="mb-4 d-print-none" position="sticky"> -->
  <c-header position="sticky">
  <c-container [fluid]="true" class="border-bottom px-4">
    <button
      (click)="toggleSidebarNarrow()"
      cHeaderToggler
      class="btn"
      style="margin-inline-start: -14px;"
      aria-label="Toggle sidebar navigation"
    >
      <svg cIcon name="cilMenu" size="lg"></svg>
    </button>
    <c-header-nav class="d-none d-md-flex">
      <c-nav-item>
        <a cNavLink routerLink="/profile" routerLinkActive="active">Profile</a>
      </c-nav-item>
    </c-header-nav>

    <c-header-nav class="d-none d-md-flex ms-auto ">
      <a cNavLink>
        <svg cIcon class="my-1" name="cilBell" size="lg"></svg>
      </a>
      <a cNavLink>
        <svg cIcon class="my-1" name="cilList" size="lg"></svg>
      </a>
      <a cNavLink>
        <svg cIcon class="my-1" name="cilEnvelopeOpen" size="lg"></svg>
      </a>
      <a cNavLink (click)="toggleCustomizer()" style="cursor: pointer;" title="Theme Customizer">
        <svg cIcon class="my-1" name="cilSettings" size="lg"></svg>
      </a>
    </c-header-nav>

    <c-header-nav class="ms-auto ms-md-0">
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
      </div>
      <ng-container *ngTemplateOutlet="themeDropdown" />
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
      </div>
    </c-header-nav>

    <c-header-nav class="mx-0">
      <ng-container *ngTemplateOutlet="userDropdown" />
    </c-header-nav>

  </c-container>
  <c-container [fluid]="true" class="px-4">
    <c-breadcrumb-router/>
  </c-container>
</c-header>

<ng-template #userDropdown>
  <c-dropdown [popperOptions]="{ placement: 'bottom-start' }" variant="nav-item">
    <button [caret]="false" cDropdownToggle class="py-0 pe-0" aria-label="Open user menu">
      <c-avatar
        shape="rounded-1"
        [size]="'md'"
        src="./assets/images/avatars/8.jpg"
        status="success"
        textColor="primary"
        alt="User avatar"
      />
    </button>
    <ul cDropdownMenu class="pt-0 w-auto">
      <li>
        <h6 cDropdownHeader class="bg-body-secondary text-body-secondary fw-semibold py-2 rounded-top">
          {{ getUserDisplayName() }}
        </h6>
      </li>
      <li *ngIf="getUserEmail()">
        <div class="px-3 py-1 text-muted small">
          {{ getUserEmail() }}
        </div>
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilBell"></svg>
          Updates
          <c-badge class="ms-2 float-end" color="info"> 42</c-badge>
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="/apps/email/inbox">
          <svg cIcon class="me-2" name="cilEnvelopeOpen"></svg>
          Messages
          <c-badge class="ms-2 float-end" color="success">
            42
          </c-badge>
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilTask"></svg>
          Tasks
          <c-badge class="ms-2 float-end" color="danger"> 42</c-badge>
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilCommentSquare"></svg>
          Comments
          <c-badge class="ms-auto" color="warning"> 42</c-badge>
        </a>
      </li>
      <li>
        <h6 cDropdownHeader class="bg-body-secondary text-body-secondary fw-semibold py-2">
          Settings
        </h6>
      </li>
      <li></li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilUser"></svg>
          Profile
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilSettings"></svg>
          Settings
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilCreditCard"></svg>
          Payments
          <c-badge class="ms-2 float-end" color="secondary">
            42
          </c-badge>
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilFile"></svg>
          Projects
          <c-badge class="ms-2 float-end" color="primary">
            42
          </c-badge>
        </a>
      </li>
      <li>
        <hr cDropdownDivider />
      </li>
      <li>
        <a cDropdownItem routerLink="">
          <svg cIcon class="me-2" name="cilLockLocked"></svg>
          Lock Account
        </a>
      </li>
      <li>
        <a cDropdownItem (click)="onLogout()" [class.disabled]="isLoggingOut" style="cursor: pointer;">
          <svg cIcon class="me-2" name="cilAccountLogout"></svg>
          <span *ngIf="!isLoggingOut">Logout</span>
          <span *ngIf="isLoggingOut">
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Logging out...
          </span>
        </a>
      </li>
    </ul>
  </c-dropdown>
</ng-template>

<ng-template #themeDropdown>
  <c-dropdown alignment="end" variant="nav-item">
    <button [caret]="false" cDropdownToggle aria-label="Open theme picker">
      <svg cIcon [name]="icons()" size="lg"></svg>
    </button>
    <div cDropdownMenu>
      @for (mode of colorModes; track mode.name) {
        <button
          (click)="colorMode.set(mode.name)"
          [active]="colorMode()===mode.name"
          cDropdownItem
          class="d-flex align-items-center"
        >
          <svg cIcon class="me-2" [name]="mode.icon" size="lg"></svg>
          {{ mode.text }}
        </button>
      }
    </div>
  </c-dropdown>
</ng-template>
